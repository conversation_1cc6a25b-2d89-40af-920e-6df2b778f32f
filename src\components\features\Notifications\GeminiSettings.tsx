import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Bell, Shield, Clock, Save, Trash2, <PERSON>ert<PERSON>riangle, X, ChevronDown, ChevronUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { But<PERSON> } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { useAuth } from '../../../hooks/useAuth';
import { GeminiNotificationSettings, NotificationPriority } from '../../../types/notifications';
import { THEME_COLORS, THEME_CLASSES } from '../../../styles/colors';
import { notificationService } from '../../../services/notificationService';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../../../services/api';
import GeminiChat from './GeminiChat';
import { BackgroundWrapper } from '../../common/BackgroundWrapper';

/**
 * Composant pour configurer les paramètres Gemini
 */
export const GeminiSettings: React.FC = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<GeminiNotificationSettings>({
    enableAutoRecommendations: true,
    checkFrequencyHours: 24,
    enableSafetyAlerts: true,
    minimumPriority: 'medium'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isCleaningNotifications, setIsCleaningNotifications] = useState(false);
  const [cleanupMessage, setCleanupMessage] = useState<string | null>(null);
  const [isMaintenanceExpanded, setIsMaintenanceExpanded] = useState(false);
  const [isDiagnosing, setIsDiagnosing] = useState(false);
  const [diagnosticResults, setDiagnosticResults] = useState<string | null>(null);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [deleteAllMessage, setDeleteAllMessage] = useState<string | null>(null);

  // Charger les paramètres existants
  useEffect(() => {
    // TODO: Charger depuis Firestore
    // loadGeminiSettings(user?.uid);
  }, [user]);

  const handleSave = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // TODO: Sauvegarder dans Firestore
      // await saveGeminiSettings(user.uid, settings);
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 3000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la sauvegarde:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanupNotifications = async () => {
    if (!user) return;

    setIsCleaningNotifications(true);
    setCleanupMessage(null);

    try {
      console.log('🧹 Début du nettoyage des notifications dupliquées...');
      await notificationService.performImmediateCleanup(user.uid);
      setCleanupMessage('✅ Nettoyage terminé avec succès ! Les notifications dupliquées ont été supprimées.');
      setTimeout(() => setCleanupMessage(null), 5000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors du nettoyage:', errorMessage);
      setCleanupMessage(`❌ Erreur lors du nettoyage: ${errorMessage}`);
      setTimeout(() => setCleanupMessage(null), 5000);
    } finally {
      setIsCleaningNotifications(false);
    }
  };

  const handleDiagnosticNotifications = async () => {
    if (!user) return;

    setIsDiagnosing(true);
    setDiagnosticResults(null);

    try {
      console.log('🔍 Diagnostic des notifications en cours...');

      // Récupérer toutes les notifications
      const notificationsSnap = await getDocs(
        query(
          collection(db, 'users', user.uid, 'notifications'),
          orderBy('createdAt', 'desc')
        )
      );

      const notifications = notificationsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Analyser les doublons
      const titleCounts: { [key: string]: number } = {};
      const duplicates: any[] = [];

      notifications.forEach(notif => {
        const title = notif.title;
        titleCounts[title] = (titleCounts[title] || 0) + 1;

        if (titleCounts[title] > 1) {
          duplicates.push({
            title,
            id: notif.id,
            createdAt: notif.createdAt?.toDate?.()?.toLocaleString() || 'Date inconnue',
            diagnosticId: notif.diagnosticId || 'Aucun'
          });
        }
      });

      const duplicateGroups = Object.entries(titleCounts)
        .filter(([, count]) => count > 1)
        .map(([title, count]) => ({ title, count }));

      const results = `
📊 DIAGNOSTIC DES NOTIFICATIONS

🔢 Total notifications: ${notifications.length}
🔄 Groupes dupliqués: ${duplicateGroups.length}
📝 Notifications dupliquées: ${duplicates.length}

${duplicateGroups.length > 0 ? `
🚨 DOUBLONS DÉTECTÉS:
${duplicateGroups.map(group => `• "${group.title}" (${group.count} fois)`).join('\n')}

📋 DÉTAILS DES DOUBLONS:
${duplicates.slice(0, 10).map(dup =>
  `• ${dup.title} (ID: ${dup.id.substring(0, 8)}..., Créé: ${dup.createdAt}, DiagID: ${dup.diagnosticId})`
).join('\n')}
${duplicates.length > 10 ? `\n... et ${duplicates.length - 10} autres` : ''}
` : '✅ Aucun doublon détecté !'}
      `;

      setDiagnosticResults(results);
      console.log('🔍 Diagnostic terminé:', results);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors du diagnostic:', errorMessage);
      setDiagnosticResults(`❌ Erreur lors du diagnostic: ${errorMessage}`);
    } finally {
      setIsDiagnosing(false);
    }
  };

  const handleDeleteAllNotifications = async () => {
    if (!user) return;

    // Demander confirmation
    if (!window.confirm('⚠️ ATTENTION : Cette action va supprimer TOUTES vos notifications de façon permanente. Êtes-vous sûr de vouloir continuer ?')) {
      return;
    }

    setIsDeletingAll(true);
    setDeleteAllMessage(null);

    try {
      console.log('🗑️ Suppression de toutes les notifications...');
      const deletedCount = await notificationService.deleteAllNotifications(user.uid);
      setDeleteAllMessage(`✅ ${deletedCount} notifications supprimées avec succès !`);
      setTimeout(() => setDeleteAllMessage(null), 5000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la suppression:', errorMessage);
      setDeleteAllMessage(`❌ Erreur lors de la suppression: ${errorMessage}`);
      setTimeout(() => setDeleteAllMessage(null), 5000);
    } finally {
      setIsDeletingAll(false);
    }
  };

  const priorityOptions: { value: NotificationPriority; label: string; color: string; buttonColor: string; borderColor: string }[] = [
    {
      value: 'low',
      label: 'Faible',
      color: 'bg-green-100 text-green-800',
      buttonColor: 'bg-green-600',
      borderColor: 'border-green-500'
    },
    {
      value: 'medium',
      label: 'Moyenne',
      color: 'bg-orange-100 text-orange-800',
      buttonColor: 'bg-orange-600',
      borderColor: 'border-orange-500'
    },
    {
      value: 'high',
      label: 'Élevée',
      color: 'bg-red-100 text-red-800',
      buttonColor: 'bg-red-600',
      borderColor: 'border-red-500'
    },
    {
      value: 'urgent',
      label: 'Urgente',
      color: 'bg-fuchsia-100 text-fuchsia-800',
      buttonColor: 'bg-fuchsia-600',
      borderColor: 'border-fuchsia-500'
    }
  ];

  const frequencyOptions = [
    { value: 24, label: '24 heures' },
    { value: 48, label: '48 heures' },
    { value: 72, label: '72 heures' },
    { value: 168, label: '1 semaine' }, // 7 jours * 24h
    { value: 360, label: '15 jours' }, // 15 jours * 24h
    { value: 504, label: '3 semaines' }, // 21 jours * 24h
    { value: 720, label: '1 mois' } // 30 jours * 24h
  ];

  return (
    <BackgroundWrapper backgroundKey="geminiSettings" overlayOpacity={0.75} scrollToTop={true}>
      <div className="max-w-7xl mx-auto p-6 pt-20">
        {/* En-tête */}
      <div className="flex items-center gap-3 mb-6">
        <Brain className="h-8 w-8 text-[#d385f5]" />
        <div>
          <h1 className="text-2xl font-bold text-white">Paramètres Gemini IA</h1>
          <p className="text-[#E0E0E0]">Configurez l'intelligence artificielle pour vos plantes et discutez avec Gemini</p>
        </div>
      </div>

      {/* Structure à deux colonnes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Colonne gauche - Paramètres */}
        <div className="space-y-6">

      {/* Statut de l'IA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-[#d385f5]" />
            Statut de l'IA
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="font-medium">IA active - Analyse vos plantes</span>
            </div>
            <div className="text-sm text-[#E0E0E0]">
              Dernière analyse: Il y a 2 heures
            </div>
          </div>
          <div className="mt-4 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
            <p className="text-sm text-[#E0E0E0]">
              🤖 L'IA Gemini analyse vos plantes en continu et génère des recommandations personnalisées
              basées sur l'historique, la saison et le type de plante.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Paramètres des recommandations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Recommandations automatiques
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Activation des recommandations */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Recommandations intelligentes</h3>
              <p className="text-sm text-gray-300">
                Génère automatiquement des conseils personnalisés
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableAutoRecommendations}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  enableAutoRecommendations: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {/* Fréquence de vérification */}
          <div>
            <h3 className="font-medium mb-2">Fréquence de vérification</h3>
            <div className="grid grid-cols-5 gap-2">
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSettings(prev => ({
                    ...prev,
                    checkFrequencyHours: option.value
                  }))}
                  className={`p-2 text-sm rounded-lg border transition-colors ${
                    settings.checkFrequencyHours === option.value
                      ? 'border-[#d385f5] bg-[#d385f5] text-white'
                      : 'border-gray-600 hover:border-[#d385f5] text-[#E0E0E0] hover:text-white'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Priorité minimum */}
          <div>
            <h3 className="font-medium mb-2">Priorité minimum des notifications</h3>
            <div className="grid grid-cols-4 gap-2">
              {priorityOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSettings(prev => ({
                    ...prev,
                    minimumPriority: option.value
                  }))}
                  className={`p-2 text-sm rounded-lg border transition-colors ${
                    settings.minimumPriority === option.value
                      ? `${option.borderColor} ${option.buttonColor} text-white`
                      : 'border-gray-600 hover:border-gray-400'
                  }`}
                >
                  <Badge className={option.color}>
                    {option.label}
                  </Badge>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alertes de sécurité */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-red-600" />
            Alertes de sécurité
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Alertes critiques</h3>
              <p className="text-sm text-gray-300">
                Notifications immédiates pour les problèmes graves (pourriture, flétrissement)
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableSafetyAlerts}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  enableSafetyAlerts: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Aperçu des fonctionnalités */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-blue-600" />
            Fonctionnalités IA activées
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Calculs d'échéances intelligents selon la saison</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Adaptation selon le type de plante</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Apprentissage basé sur l'historique</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Détection des patterns de maladies récurrentes</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Recommandations préventives personnalisées</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section de maintenance (accordéon) */}
      <Card className="bg-[#1c1a31] border-[#d385f5]/30">
        <CardHeader
          className="cursor-pointer hover:bg-[#2a2847] transition-colors"
          onClick={() => setIsMaintenanceExpanded(!isMaintenanceExpanded)}
        >
          <CardTitle className="flex items-center justify-between text-[#d385f5]">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Maintenance et Nettoyage
            </div>
            {isMaintenanceExpanded ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </CardTitle>
        </CardHeader>

        {isMaintenanceExpanded && (
          <CardContent className="space-y-4 border-t border-[#d385f5]/20">
          <div className="p-4 bg-[#2a2847] border border-[#d385f5]/30 rounded-lg">
            <p className="text-sm text-[#E0E0E0] mb-3">
              <strong className="text-[#d385f5]">Action de maintenance :</strong> Cette fonction supprime les notifications dupliquées
              qui peuvent s'être accumulées dans votre compte. Elle ne supprime qu'une seule occurrence
              de chaque notification en gardant la plus récente.
            </p>
            <p className="text-xs text-[#9CA3AF]">
              ⚠️ Cette section sera supprimée après résolution du problème de doublons.
            </p>
          </div>

          {cleanupMessage && (
            <div className={`p-3 rounded-lg ${
              cleanupMessage.startsWith('✅')
                ? 'bg-green-900/20 border border-green-500/30 text-green-200'
                : 'bg-red-900/20 border border-red-500/30 text-red-200'
            }`}>
              {cleanupMessage}
            </div>
          )}

          {diagnosticResults && (
            <div className="p-3 rounded-lg bg-[#2a2847] border border-[#d385f5]/30 text-[#E0E0E0]">
              <pre className="text-xs whitespace-pre-wrap font-mono">
                {diagnosticResults}
              </pre>
            </div>
          )}

          {deleteAllMessage && (
            <div className={`p-3 rounded-lg ${
              deleteAllMessage.startsWith('✅')
                ? 'bg-green-900/20 border border-green-500/30 text-green-200'
                : 'bg-red-900/20 border border-red-500/30 text-red-200'
            }`}>
              {deleteAllMessage}
            </div>
          )}

          <div className="space-y-3">
            <div className="flex gap-3">
              <Button
                onClick={handleDiagnosticNotifications}
                disabled={isDiagnosing}
                className="flex-1 bg-[#d385f5] hover:bg-[#c070e0] text-white"
              >
                {isDiagnosing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                    Diagnostic...
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Diagnostiquer les doublons
                  </>
                )}
              </Button>

              <Button
                onClick={handleCleanupNotifications}
                disabled={isCleaningNotifications}
                className="flex-1 bg-[#a364f7] hover:bg-[#9333ea] text-white"
              >
                {isCleaningNotifications ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                    Nettoyage...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Nettoyer les doublons
                  </>
                )}
              </Button>
            </div>

            <Button
              onClick={handleDeleteAllNotifications}
              disabled={isDeletingAll}
              className="w-full bg-[#7F1D1D] hover:bg-[#991B1B] text-white border border-[#EF4444]/30"
            >
              {isDeletingAll ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Suppression en cours...
                </>
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Supprimer TOUTES les notifications
                </>
              )}
            </Button>
          </div>
          </CardContent>
        )}
      </Card>

          {/* Bouton de sauvegarde */}
          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className={`flex items-center gap-2 ${
                isSaved ? 'bg-[#d385f5] hover:bg-[#c070e0]' : 'bg-[#d385f5] hover:bg-[#c070e0]'
              } text-white`}
            >
              {isLoading ? (
                <Clock className="h-4 w-4 animate-spin" />
              ) : isSaved ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
                  </div>
                  Sauvegardé
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Sauvegarder
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Colonne droite - Chat avec Gemini */}
        <div className="lg:sticky lg:top-24">
          <GeminiChat />
        </div>
      </div>
      </div>
    </BackgroundWrapper>
  );
};

export default GeminiSettings;
